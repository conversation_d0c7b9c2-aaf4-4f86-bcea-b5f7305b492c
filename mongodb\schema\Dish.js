const mongoose = require("mongoose");

const dishSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    price: {
      type: Number,
      required: true,
    },
    discount: {
      type: Number,
    },
    description: {
      type: String,
      required: true,
    },
    diningLocation: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "DiningLocation",
    },
    offering: {
      type: [String],
      enum: ["Dine In", "Take Away", "Delivery"],
    },
    ratings: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Rating",
      },
    ],
    averageRating: {
      type: Number,
      default: 0,
    },
    photos: {
      type: [String],
      validate: [arrayMinSize, "A dish must have at least one photo"],
      required: true,
    },
    ingredients: {
      type: [String],
      validate: [arrayMinSize, "A dish must have at least one ingredient"],
      required: true,
    },
    cuisine: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Cuisine",
      required: true,
    },
    subcuisine: {
      type: String,
    },
    published: {
      type: Boolean,
      default: false,
    },
    minServings: {
      type: Number,
      required: true,
    },
    maxServings: {
      type: Number,
      required: true,
    },
    premade: {
      type: Boolean,
      default: false,
    },
    request: {
      type: Boolean,
      default: false,
    },
    currency: {
      type: String,
      default: "USD",
    },

    availability: [
      {
        date: {
          type: Date,
          required: true,
        },
        startTime: {
          type: String,
          required: true,
          validate: {
            validator: (v) => /^([01]\d|2[0-3]):([0-5]\d)$/.test(v),
            message: (props) =>
              `${props.value} is not a valid time format. Use HH:MM.`,
          },
        },
        endTime: {
          type: String,
          required: true,
          validate: {
            validator: (v) => /^([01]\d|2[0-3]):([0-5]\d)$/.test(v),
            message: (props) =>
              `${props.value} is not a valid time format. Use HH:MM.`,
          },
        },
        startTimeGMT: {
          type: Date,
          default: null,
        },
        endTimeGMT: {
          type: Date,
          default: null,
        },
      },
    ],
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Validator for minimum array length
function arrayMinSize(val) {
  return val.length >= 1;
}

// Removed automatic population pre-hook to allow selective filtering
// Population will be handled explicitly in service layer

// Validator to ensure endTime is after startTime
dishSchema.path("availability").validate((value) => {
  if (value && value.length) {
    return value.every((slot) => {
      const start = new Date(`1970-01-01T${slot.startTime}:00`);
      const end = new Date(`1970-01-01T${slot.endTime}:00`);
      return end > start;
    });
  }
  return true;
}, "End time must be after start time");

module.exports = dishSchema;
